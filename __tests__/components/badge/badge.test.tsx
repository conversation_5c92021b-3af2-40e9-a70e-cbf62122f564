import { render, screen } from "@testing-library/react";
import { describe, it, expect } from "vitest";
import { Badge, type BadgeConfig } from "../../../src/components/badge/badge";

// Test configuration
type TestType = "success" | "warning" | "error";

const TEST_CONFIG: Record<TestType, BadgeConfig> = {
  success: {
    label: "SUCCESS",
    icon: "success-icon.png",
    iconAlt: "Success Icon",
    containerClasses: "border-green-500 bg-green-100",
    textClasses: "text-green-700",
  },
  warning: {
    label: "WARNING",
    icon: "warning-icon.png", 
    iconAlt: "Warning Icon",
    containerClasses: "border-yellow-500 bg-yellow-100",
    textClasses: "text-yellow-700",
  },
  error: {
    label: "ERROR",
    icon: "error-icon.png",
    iconAlt: "Error Icon", 
    containerClasses: "border-red-500 bg-red-100",
    textClasses: "text-red-700",
  },
};

describe("Generic Badge Component", () => {
  it("should render nothing when no type is provided", () => {
    const { container } = render(<Badge config={TEST_CONFIG} />);
    expect(container.firstChild).toBeNull();
  });

  it("should render success badge correctly", () => {
    render(<Badge type="success" config={TEST_CONFIG} />);

    expect(screen.getByText("SUCCESS")).toBeInTheDocument();
    expect(screen.getByAltText("Success Icon")).toBeInTheDocument();
    expect(screen.getByText("SUCCESS")).toHaveClass("text-green-700");
  });

  it("should render warning badge correctly", () => {
    render(<Badge type="warning" config={TEST_CONFIG} />);

    expect(screen.getByText("WARNING")).toBeInTheDocument();
    expect(screen.getByAltText("Warning Icon")).toBeInTheDocument();
    expect(screen.getByText("WARNING")).toHaveClass("text-yellow-700");
  });

  it("should render error badge correctly", () => {
    render(<Badge type="error" config={TEST_CONFIG} />);

    expect(screen.getByText("ERROR")).toBeInTheDocument();
    expect(screen.getByAltText("Error Icon")).toBeInTheDocument();
    expect(screen.getByText("ERROR")).toHaveClass("text-red-700");
  });

  it("should apply custom className", () => {
    const { container } = render(
      <Badge type="success" config={TEST_CONFIG} className="custom-class" />
    );

    expect(container.firstChild).toHaveClass("custom-class");
  });

  it("should apply base classes correctly", () => {
    const { container } = render(<Badge type="success" config={TEST_CONFIG} />);
    const badge = container.firstChild as HTMLElement;

    expect(badge).toHaveClass(
      "flex",
      "w-fit", 
      "items-center",
      "gap-1",
      "rounded-lg",
      "border",
      "px-2"
    );
  });

  it("should apply configuration classes correctly", () => {
    const { container } = render(<Badge type="success" config={TEST_CONFIG} />);
    const badge = container.firstChild as HTMLElement;

    expect(badge).toHaveClass("border-green-500", "bg-green-100");
  });

  it("should render icon with correct src and alt", () => {
    render(<Badge type="warning" config={TEST_CONFIG} />);
    
    const icon = screen.getByAltText("Warning Icon") as HTMLImageElement;
    expect(icon.src).toContain("warning-icon.png");
    expect(icon).toHaveClass("size-4");
  });

  it("should render text with correct classes", () => {
    render(<Badge type="error" config={TEST_CONFIG} />);
    
    const text = screen.getByText("ERROR");
    expect(text).toHaveClass("font-semibold", "text-sm", "text-red-700");
  });

  it("should work with different badge types", () => {
    const types: TestType[] = ["success", "warning", "error"];
    
    types.forEach((type) => {
      const { container, unmount } = render(
        <Badge type={type} config={TEST_CONFIG} />
      );
      
      expect(container.firstChild).toBeInTheDocument();
      expect(screen.getByText(TEST_CONFIG[type].label)).toBeInTheDocument();
      expect(screen.getByAltText(TEST_CONFIG[type].iconAlt)).toBeInTheDocument();
      
      unmount();
    });
  });
});
