import { cn } from "@utils/cn";

interface BadgeConfig {
  label?: string;
  icon?: string;
  iconAlt?: string;
  containerClasses?: string;
  textClasses?: string;
}

interface BadgeProps<T extends string> {
  type?: T;
  className?: string;
  config: Record<T, BadgeConfig>;
}

const BASE_BADGE_CLASSES =
  "flex w-fit items-center gap-1 rounded-lg border px-2";
const BASE_ICON_CLASSES = "size-4";
const BASE_TEXT_CLASSES = "font-semibold text-sm";

export const Badge = <T extends string>({
  type,
  className,
  config,
}: BadgeProps<T>) => {
  if (!type) {
    return null;
  }

  const badgeConfig = config[type];

  return (
    <div
      className={cn(
        BASE_BADGE_CLASSES,
        badgeConfig.containerClasses,
        className
      )}
    >
      <img
        src={badgeConfig.icon}
        alt={badgeConfig.iconAlt}
        className={BASE_ICON_CLASSES}
      />

      {/* <div
        className={cn(
          BASE_BADGE_CLASSES,
          badgeConfig.containerClasses,
          className
        )}
      >
        <i className={cn(badgeConfig.icon)} />
      </div> */}
      <span className={cn(BASE_TEXT_CLASSES, badgeConfig.textClasses)}>
        {badgeConfig.label}
      </span>
    </div>
  );
};

// Export the BadgeConfig type for reuse
export type { BadgeConfig };
