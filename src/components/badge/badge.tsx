import { cn } from "@utils/cn";

interface BadgeProps {
  type?: <T>;
  className?: string;
}

const BASE_BADGE_CLASSES =
  "flex w-fit items-center gap-1 rounded-lg border px-2";
const BASE_ICON_CLASSES = "size-4";
const BASE_TEXT_CLASSES = "font-semibold text-sm";

export const Badge = ({ type, className }: BadgeProps) => {
  if (!type) {
    return null;
  }

  const config = <T>[type];

  return (
    <div className={cn(BASE_BADGE_CLASSES, config.containerClasses, className)}>
      <img
        src={config.icon}
        alt={config.iconAlt}
        className={BASE_ICON_CLASSES}
      />
      <span className={cn(BASE_TEXT_CLASSES, config.textClasses)}>
        {config.label}
      </span>
    </div>
  );
};
