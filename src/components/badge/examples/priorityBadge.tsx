import { Badge, type BadgeConfig } from "@components/badge/badge";
import highIcon from "@assets/high-priority.png";
import mediumIcon from "@assets/medium-priority.png";
import lowIcon from "@assets/low-priority.png";

// Define priority types
export type PriorityType = "high" | "medium" | "low";

// Define priority badge configuration
interface PriorityBadgeConfig extends BadgeConfig {
  type: PriorityType;
}

const PRIORITY_BADGE_CONFIG: Record<PriorityType, PriorityBadgeConfig> = {
  high: {
    type: "high",
    label: "HIGH",
    icon: highIcon,
    iconAlt: "High Priority",
    containerClasses: "border-error bg-error/10",
    textClasses: "text-error",
  },
  medium: {
    type: "medium",
    label: "MEDIUM",
    icon: mediumIcon,
    iconAlt: "Medium Priority",
    containerClasses: "border-warning bg-warning/10",
    textClasses: "text-warning",
  },
  low: {
    type: "low",
    label: "LOW",
    icon: lowIcon,
    iconAlt: "Low Priority",
    containerClasses: "border-info bg-info/10",
    textClasses: "text-info",
  },
};

// Priority badge component using the generic Badge
interface PriorityBadgeProps {
  type?: PriorityType;
  className?: string;
}

export const PriorityBadge = ({ type, className }: PriorityBadgeProps) => {
  return (
    <Badge
      type={type}
      className={className}
      config={PRIORITY_BADGE_CONFIG}
    />
  );
};
