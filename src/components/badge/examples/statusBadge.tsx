import { Badge, type BadgeConfig } from "@components/badge/badge";
import checkIcon from "@assets/check.png";
import clockIcon from "@assets/clock.png";
import xIcon from "@assets/x.png";

// Define status types
export type StatusType = "success" | "pending" | "failed";

// Define status badge configuration
interface StatusBadgeConfig extends BadgeConfig {
  type: StatusType;
}

const STATUS_BADGE_CONFIG: Record<StatusType, StatusBadgeConfig> = {
  success: {
    type: "success",
    label: "SUCCESS",
    icon: checkIcon,
    iconAlt: "Check",
    containerClasses: "border-success bg-success/10",
    textClasses: "text-success",
  },
  pending: {
    type: "pending", 
    label: "PENDING",
    icon: clockIcon,
    iconAlt: "Clock",
    containerClasses: "border-warning bg-warning/10",
    textClasses: "text-warning",
  },
  failed: {
    type: "failed",
    label: "FAILED", 
    icon: xIcon,
    iconAlt: "X",
    containerClasses: "border-error bg-error/10",
    textClasses: "text-error",
  },
};

// Status badge component using the generic Badge
interface StatusBadgeProps {
  type?: StatusType;
  className?: string;
}

export const StatusBadge = ({ type, className }: StatusBadgeProps) => {
  return (
    <Badge
      type={type}
      className={className}
      config={STATUS_BADGE_CONFIG}
    />
  );
};
