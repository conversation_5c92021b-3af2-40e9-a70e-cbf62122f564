import cloudy from "@assets/cloudy.png";
import moon from "@assets/moon.png";
import sun from "@assets/sun.png";
import { cn } from "@utils/cn";

export type OpportunityType = "hot" | "warm" | "cold";

interface OpportunityBadgeConfig {
  type: OpportunityType;
  label: string;
  icon: string;
  iconAlt: string;
  containerClasses: string;
  textClasses: string;
}

const OPPORTUNITY_BADGE_CONFIG: Record<
  OpportunityType,
  OpportunityBadgeConfig
> = {
  cold: {
    containerClasses: "border-primary bg-success-content",
    icon: cloudy,
    iconAlt: "Cloudy",
    label: "COLD",
    textClasses: "text-primary",
    type: "cold",
  },
  hot: {
    containerClasses: "border-secondary bg-secondary-content/30",
    icon: sun,
    iconAlt: "Sun",
    label: "HOT",
    textClasses: "text-error-content",
    type: "hot",
  },
  warm: {
    containerClasses: "border-warning-content bg-accent/30",
    icon: moon,
    iconAlt: "Moon",
    label: "WARM",
    textClasses: "text-warning-content",
    type: "warm",
  },
};

const BASE_BADGE_CLASSES =
  "flex w-fit items-center gap-1 rounded-lg border px-2";
const BASE_ICON_CLASSES = "size-4";
const BASE_TEXT_CLASSES = "font-semibold text-sm";

interface OpportunityBadgeProps {
  type?: OpportunityType;
  className?: string;
}

export const OpportunityBadge = ({
  type,
  className,
}: OpportunityBadgeProps) => {
  if (!type) {
    return null;
  }

  const config = OPPORTUNITY_BADGE_CONFIG[type];

  return (
    <div className={cn(BASE_BADGE_CLASSES, config.containerClasses, className)}>
      <img
        src={config.icon}
        alt={config.iconAlt}
        className={BASE_ICON_CLASSES}
      />
      <span className={cn(BASE_TEXT_CLASSES, config.textClasses)}>
        {config.label}
      </span>
    </div>
  );
};
