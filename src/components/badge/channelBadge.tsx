import { cn } from "@utils/cn";
import { Badge } from "./badge";

export type ChanneType = "facebook" | "line" | "instagram" | "tiktok";

const FACEBOOK_ICON = <i className="ri-facebook-circle-fill" />;

interface ChannelBadgeConfig {
  type: ChanneType;
  icon: string;
  iconAlt: string;
  containerClasses: string;
}

const CHANNEL_BADGE_CONFIG: Record<ChanneType, ChannelBadgeConfig> = {
  facebook: {
    containerClasses: "text-blue-500",
    icon: FACEBOOK_ICON,
    iconAlt: "Facebook",
    type: "facebook",
  },
  instagram: {
    containerClasses: "text-pink-500",
    icon: "ri-instagram-fill",
    iconAlt: "Instagram",
    type: "instagram",
  },
  line: {
    containerClasses: "text-green-500",
    icon: "ri-line-fill",
    iconAlt: "Line",
    type: "line",
  },
  tiktok: {
    containerClasses: "text-black",
    icon: "ri-tiktok-fill",
    iconAlt: "Tiktok",
    type: "tiktok",
  },
};

const BASE_BADGE_CLASSES = "flex items-center aspect-square text-2xl";

interface ChannelBadgeProps {
  type?: ChanneType;
  className?: string;
}

const icon = () => {
  return (
    <div className={cn(BASE_BADGE_CLASSES, config.containerClasses, className)}>
      <i className={cn(config.icon)} />
    </div>
  );
};

export const ChannelBadge = ({ type, className }: ChannelBadgeProps) => {
  return (
    <Badge type={type} className={className} config={CHANNEL_BADGE_CONFIG} />
  );
};
